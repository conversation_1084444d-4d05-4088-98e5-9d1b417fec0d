# 📘 Functional Requirements – English Learning & Course Sales Platform

## 🧩 Tổng Quan

Hệ thống bao gồm 2 thành phần chính:
- **<PERSON><PERSON> App**: <PERSON><PERSON> lý bán kh<PERSON><PERSON> học, ng<PERSON><PERSON><PERSON> dùng, unlock mã học, g<PERSON><PERSON> AI chấm điểm
- **Moodle LMS**: <PERSON><PERSON><PERSON> vụ học và làm bài (Reading, Listening, Writing, Speaking)

---

## 1. 📚 Quản lý Khóa học

### 1.1 Tạo và quản lý khóa học
- [ ] <PERSON><PERSON> có thể tạo, sử<PERSON>, xóa khóa học
- [ ] Kh<PERSON><PERSON> học gồm: tê<PERSON>, mô tả, ảnh đại diện, gi<PERSON>, tr<PERSON><PERSON> thái (công khai / ẩn)

### 1.2 Gán khóa học với Moodle
- [ ] Mỗi khóa học Laravel mapping với 1 khóa học trên <PERSON>
- [ ] <PERSON><PERSON> thể nhập `moodle_course_id` để liên kết

---

## 2. 💰 <PERSON><PERSON> thống <PERSON>án khóa học & Mã khóa

### 2.1 Mã unlock khóa học
- [ ] Admin có thể tạo danh sách mã học: mã code, số lần sử dụng, ngày hết hạn
- [ ] Người dùng nhập mã → nếu hợp lệ → unlock khóa học

### 2.2 Thanh toán online (tùy chọn)
- [ ] Tích hợp VNPay / Stripe (tùy chọn giai đoạn 2)
- [ ] Sau khi thanh toán thành công → tự động unlock khóa học

---

## 3. 👤 Tài khoản người dùng

### 3.1 Đăng ký / Đăng nhập
- [ ] Laravel Breeze hoặc Jetstream
- [ ] Xác thực qua email hoặc OTP (tùy chọn)

### 3.2 Role người dùng
- [ ] Học viên
- [ ] Quản trị viên
- [ ] Giáo viên (tùy chọn giai đoạn sau)

---

## 4. 🎓 Lộ trình học & Luyện đề

### 4.1 Phân loại kỹ năng
- [ ] Mỗi khóa học có các module theo 4 kỹ năng: Reading, Listening, Writing, Speaking
- [ ] Điều hướng người dùng từ Laravel sang Moodle theo đúng module

### 4.2 Học qua Moodle
- [ ] Laravel gọi API tạo user bên Moodle (nếu chưa có)
- [ ] Gán user vào khóa học Moodle qua enroll API
- [ ] Laravel redirect học viên sang Moodle (hoặc nhúng iframe)

---

## 5. 🤖 AI Chấm điểm tự động (Laravel xử lý)

### 5.1 Writing
- [ ] Laravel gọi API Moodle để lấy nội dung bài viết
- [ ] Gửi nội dung lên LanguageTool hoặc GPT để chấm điểm
- [ ] Lưu điểm + nhận xét vào DB
- [ ] Hiển thị kết quả cho học viên

### 5.2 Speaking
- [ ] Lấy file audio từ Moodle (nộp qua quiz/plugin ghi âm)
- [ ] Gửi file qua Whisper API để lấy transcript
- [ ] Tính điểm theo rubric
- [ ] Lưu và hiển thị kết quả

---

## 6. 📊 Dashboard

### 6.1 Dashboard Học viên
- [ ] Hiển thị danh sách khóa học đã unlock
- [ ] Hiển thị tiến độ, điểm số theo kỹ năng
- [ ] Chi tiết từng bài Writing/Speaking đã nộp và điểm

### 6.2 Dashboard Admin
- [ ] Thống kê học viên, số lượt học, điểm trung bình
- [ ] Quản lý các bài nộp AI (xem chi tiết, sửa điểm thủ công)
- [ ] Quản lý danh sách mã unlock

---

## 7. 🔐 Bảo mật & Giới hạn

- [ ] Laravel Rate limiting API AI
- [ ] Xác thực email đăng ký
- [ ] Học viên chỉ xem được bài và điểm của mình

---

## 8. 🧠 Tích hợp Moodle

| Tác vụ                              | Yêu cầu chức năng |
|------------------------------------|-------------------|
| Tạo user Moodle                    | Laravel gọi API `core_user_create_users` |
| Gán user vào khóa học              | API `enrol_manual_enrol_users` |
| Lấy bài Writing                    | API `mod_assign_get_submissions` |
| Lấy file Speaking (audio)          | API `core_files_get_files` |
| Lấy điểm quiz Reading/Listening    | API `gradereport_user_get_grades_table` |
| Gửi điểm lại Moodle (tùy chọn)     | API `grade_update` (nếu muốn) |

---

## 9. ⚙️ Hệ thống hàng đợi AI (Queue)

- [ ] Laravel sử dụng Queue để xử lý AI chấm bài
- [ ] Tạo Job riêng cho mỗi loại: `EvaluateWritingJob`, `EvaluateSpeakingJob`
- [ ] Kết quả lưu vào bảng `results`

---

## 10. 💾 Database Schema (cơ bản)

| Bảng               | Mô tả |
|--------------------|------|
| `users`            | Học viên / admin |
| `courses`          | Khóa học |
| `access_codes`     | Mã unlock |
| `user_courses`     | Gán học viên vào khóa |
| `assignments`      | Bài viết / ghi âm |
| `results`          | Điểm bài nộp |
| `moodle_accounts`  | Mapping user Laravel ↔ Moodle |

---

## 📌 Ghi chú triển khai

- Laravel là trung tâm điều phối và xử lý logic
- Moodle chỉ đóng vai trò LMS (học và nộp bài)
- Ưu tiên viết code sạch, dễ scale, sử dụng Laravel best practices
- Queue + API cần có retry / xử lý lỗi

---

## 🚀 Giai đoạn triển khai

| Giai đoạn | Mục tiêu |
|----------|----------|
| G1       | Login, khóa học, mã unlock, dashboard |
| G2       | Tích hợp Moodle + học thử |
| G3       | Gọi AI Writing + Speaking |
| G4       | Dashboard điểm học viên |
| G5       | Triển khai thật, theo dõi + tối ưu |

---

